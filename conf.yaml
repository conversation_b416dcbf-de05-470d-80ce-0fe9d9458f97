app:
  env: dev
  debug: true
  cookie:
    path: /
    domain: ************
    max_age: 2592000
    secure: false
    http_only: false
    same_site: 1
  locker:
    disabled: false
    redis_name: default
    times: 3
    timeout: 5s
  server:
    socket_address: 0.0.0.0
    socket_port: 8999
    assets:
      - path: /res
        root: src/http/res
#  id_generator:
#    redis_name: default
#    worker_id_prefix: "APP_IDG:"
  router:
    white_list:
      - /api/v1/auth/login
      - /api/v1/rev/mock/sender-provider/get
      - /api/v1/sys/resources
      - /api/v1/platform/audit_notify
  sender:
    upload:
      dst: resources/hk-box/sender/upload
      remote_url: http://*************:30002/api/v1/trans_files
  rev:
    fisco:
      tars:
        src: resources/hk-box/rev/tars
        archive: resources/hk-box/rev/archive
      sign_server_url: http://************:8889
      sign_server_weid: did:weid:101:0xb0f1816a9cdc68a378ce97bb24eacf4991e09e3a
      sign_server_password: 123456
      webhook:
        - addr: http://***********:8888/api/v1/receive_notify
          enabled: false
      watermark_api:
        temp_dir: /tmp/hk-box-watermark-tmp
        addr: http://127.0.0.1:30002/api/v1/add_watermark
        enabled: false
  platform:
    report_url: http://************:8889/api/v1/report_notify
    local_ip: ************
    local_port: 8888
    enabled_report: false
  transport_channel:
    api_addr: http://127.0.0.1:30002/api/v1/channel/channel_info
pkg:
  database:
    - name: default
      driver: mysql #数据库驱动
      host: ************ #服务器地址
      port: 8980 #服务器端口
      user: root #数据库用户
      pwd: rootroot #数据库密码
      dbname: hk_box #数据库名称，不设置数据库名，使用所有数据库
      charset: utf8mb4 #编码
      collation: utf8mb4_general_ci #字符集
  redis:
    - name: default
      addrs:
        - "************:8982"
      db: 1
      username: ~
      password: 123456
      sentinel_password: ~
      max_retries: 3
      pool_fifo: false
      pool_size: 0
      min_idle_conns: 0
      max_redirects: 0
      readonly: false
      route_by_latency: false
      route_randomly: false
      master_mame: ~
      check: true
#  kafka:
#    producer:
#      - name: default
#        config_map:
#          bootstrap.servers: ************:9092
#    consumer:
#      - name: default
#        config_map:
#          bootstrap.servers: ************:9092
#          group.id: hk_box
#          auto.offset.reset: earliest
#  #      subscribe_topics:
#  #        - hkPackageReceiver
#  #        read_message_timeout: 5s
#  elasticsearch:
#    - name: default
#      config:
#        Addresses:
#          - http://************:9200
#        EnableDebugLogger: true
#        Username: elastic
#        Password: eseses
  cors:
    allowed_origins:
      - "http://localhost:3005"
      - "http://************:8005"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "PATCH"
      - "DELETE"
      - "HEAD"
    allowed_headers:
      - "Origin"
      - "Content-Length"
      - "Content-Type"
      - "X-Requested-With"
      - "Authorization"
      - "Withcredentials"
      - "*"
    allow_credentials: true
    expose_headers:
      - "Content-Disposition"
    max_age: 86400
    allow_websockets: true
  logger:
    - name: default
      file_log:
        filepath: /home/<USER>/projects/hk-box-be/logs/app.log