package v1

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"os"
	"strings"
)

// SysConfig
// @Router /api/v1/sys-config []
type SysConfig struct {
}

// Get
// @Router /get [get]
func (s *SysConfig) Get(ctx *gin.Context) (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		return nil, fmt.Errorf("adminUser not found")
	}
	operator := authutil.GetUsername(ctx)

	return map[string]any{
		"rev_dl_wm_enabled":    m.RevDlWmEnabled,
		"sender_up_wm_enabled": m.SenderUpWmEnabled,
		"institution":          adminUser.Institution,
		"operator":             operator,
	}, nil
}

// Update
// @Router /update [put]
func (s *SysConfig) Update(ctx *gin.Context, req *request.UpdateSysConfigRequest) error {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	if req == nil {
		req = &request.UpdateSysConfigRequest{}
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	updateFields := make([]string, 0)
	if req.RevDlWmEnabled != nil {
		updateFields = append(updateFields, "RevDlWmEnabled")
		m.RevDlWmEnabled = *req.RevDlWmEnabled
	}
	if req.SenderUpWmEnabled != nil {
		updateFields = append(updateFields, "SenderUpWmEnabled")
		m.SenderUpWmEnabled = *req.SenderUpWmEnabled
	}
	if err = orm.Model(m).Select(updateFields).Updates(m).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	return nil
}

// GetRemoteUploadConfig
// @Router /get-remote-upload-config [get]
func (s *SysConfig) GetRemoteUploadConfig(ctx *gin.Context) (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if m.Sftp == nil {
		m.Sftp = &model.SysConfigSftp{
			Enabled: false,
			Auth: model.SysConfigSftpAuth{
				Type:           0,
				Port:           22,
				Username:       "root",
				PrivateKeyType: 0,
				Timeout:        10000,
			},
			Upload: model.SysConfigSftpUpload{
				DstDir:           "/root/hk-box",
				AllowedFileTypes: []string{".pdf"},
			},
		}
	}
	if m.Dicom == nil {
		m.Dicom = &model.SysConfigDicom{
			Enabled: false,
			Auth: model.SysConfigDicomAuth{
				ServerPort: 104,
			},
			Upload: model.SysConfigDicomUpload{
				AllowedFileTypes: []string{".dcm"},
			},
		}
	}

	type SecretPair struct {
		Type          string `json:"type"`
		PubKeyPath    string `json:"pub_key_path"`
		PubKeyContent string `json:"pub_key_content"`
		PriKeyPath    string `json:"pri_key_path"`
		PriKeyContent string `json:"pri_key_content"`
	}
	secretPairs := make([]SecretPair, 0)

	homeDir, err := os.UserHomeDir()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	checkNames := []string{"id_rsa", "id_ed25519", "ecdsa"}
	for _, name := range checkNames {
		if priv, err := os.ReadFile(homeDir + "/.ssh/" + name); err == nil {
			if pubk, err := os.ReadFile(homeDir + "/.ssh/" + name + ".pub"); err == nil {
				secretPairs = append(secretPairs, SecretPair{
					Type:          strings.TrimLeft(name, "id_"),
					PubKeyPath:    homeDir + "/.ssh/" + name + ".pub",
					PubKeyContent: strings.TrimSpace(string(pubk)),
					PriKeyPath:    homeDir + "/.ssh/" + name,
					PriKeyContent: strings.TrimSpace(string(priv)),
				})
			}
		}
	}

	return map[string]any{
		"sftp":         m.Sftp,
		"dicom":        m.Dicom,
		"secret_pairs": secretPairs,
	}, nil
}

// UpdateRemoteUploadConfig
// @Router /update-remote-upload-config [put]
func (s *SysConfig) UpdateRemoteUploadConfig(ctx *gin.Context, req *request.UpdateSysConfigRemoteUploadConfig) error {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	if req == nil {
		req = &request.UpdateSysConfigRemoteUploadConfig{}
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	updateFields := make([]string, 0)
	if req.SFTP != nil {
		updateFields = append(updateFields, "sftp")
		m.Sftp = req.SFTP
	}
	if req.Dicom != nil {
		updateFields = append(updateFields, "dicom")
		// 这里直接硬编码dcm类型，目前不需要自定义扩展类型
		req.Dicom.Upload.AllowedFileTypes = []string{".dcm"}
		m.Dicom = req.Dicom
	}
	if err = orm.Model(m).Select(updateFields).Updates(m).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	return nil
}
