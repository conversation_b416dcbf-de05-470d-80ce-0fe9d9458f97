.wrapper:global {
  height: 100%;
  overflow: hidden;

  >.wrapper-inner {
    height: 100%;
    overflow: hidden;

    .page-layout {
      background: none;

      >.page-container {
        background: none;
        padding: 12px 12px;

        >.container-inner {
          box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          //box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          display: flex;
          flex-direction: column;

          >.container-body {
            flex: 1;
            position: relative;
            padding: 24px 48px;
            padding-left: 100px;

            >.ant-form {

              //width: 620px;
              //margin: 0 auto;
              .ant-form-item {
                .watermark-explain {
                  margin-top: 8px;
                  font-size: 12px;

                  >.watermark-explain-head {
                    >h2 {
                      font-weight: bold;
                      color: #3366ff;
                    }

                    >h3 {
                      //color: #606266;
                      //margin-left: 12px;
                    }
                  }

                  >.watermark-explain-body {
                    >h3 {
                      color: #3366ff;
                      font-weight: bold;
                    }

                    >ul {
                      list-style: circle;
                      margin-left: 18px;

                      >li {
                        line-height: 24px;
                      }
                    }
                  }
                }
              }

              .actions {
                .act-submit {
                  height: auto;
                  padding: 8px 36px;
                }
              }

              .sftp-config {
                .ant-card {

                  &.auth-config,
                  &.upload-config {
                    width: 680px;

                    >.ant-card-head {
                      border-bottom: none;
                    }
                  }

                  &.upload-config {
                    margin-top: 12px;
                  }
                }
              }

              .dicom-config {
                .ant-card {

                  &.auth-config,
                  &.upload-config {
                    width: 680px;

                    >.ant-card-head {
                      border-bottom: none;
                    }
                  }

                  &.upload-config {
                    margin-top: 12px;
                  }
                }
              }

              .ip-list {
                width: 630px;
              }
            }
          }
        }
      }
    }
  }
}