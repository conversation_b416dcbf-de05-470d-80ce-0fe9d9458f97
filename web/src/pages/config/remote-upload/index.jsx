import styles from './index.module.scss'
import PageLayout from "@/components/PageLayout";
import {
    App,
    Button,
    Card,
    Form,
    Input,
    InputNumber,
    List,
    Modal,
    Radio,
    Switch
} from "antd";
import Fieldset from "@/components/Fieldset";
import { useContext, useEffect } from "react";
import { GetSysRemoteUploadConfig, UpdateSysRemoteUploadConfig } from "@/api/sys_config";
import TransferInstance from "@/utils/app/transfer";
import { cloneDeep, get, isArray, join, set, split, uniq } from "lodash";
import { useTranslation } from "react-i18next";
import { useFormRules } from './index.metadata'
import { useImmer } from "use-immer";


const SftpModalForm = ({ form, rules, t }) => {
    return (
        <Form form={form} layout="vertical">
            <Form.Item name="host" label={t('sftp host')} rules={rules.SFtp.Auth.Host}>
                <Input variant={'filled'} placeholder={t('please input the sftp host')} />
            </Form.Item>
            <Form.Item name="port" label={t('sftp port')} rules={rules.SFtp.Auth.Port}>
                <InputNumber variant={'filled'} min={0} precision={0} style={{ width: '100%' }} controls={false}
                    placeholder={t('sftp port')} />
            </Form.Item>
            <Form.Item name="username" label={t('sftp username')} rules={rules.SFtp.Auth.Username}>
                <Input variant={'filled'} placeholder={t('sftp username')} />
            </Form.Item>
            <Form.Item name="timeout" label={t('sftp connect timeout')} rules={rules.SFtp.Auth.Timeout}>
                <InputNumber variant={'filled'} min={0} precision={0} style={{ width: '100%' }} suffix={'ms'} controls={false}
                    placeholder={t('sftp connect timeout')} />
            </Form.Item>
            <Form.Item name="type" label={t('authentication method')} rules={rules.SFtp.Auth.Type}>
                <Radio.Group buttonStyle={'solid'}>
                    <Radio.Button value={0}>{t('password authentication')}</Radio.Button>

                    <Radio.Button value={1}>{t('private key authentication')}</Radio.Button>
                </Radio.Group>
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(prev, curr) => get(prev, 'type') !== get(curr, 'type')}>
                {({ getFieldValue }) => {
                    const type = getFieldValue('type');
                    if (type === 0) {
                        return (
                            <Form.Item name="password" label={t('sftp password')} rules={rules.SFtp.Auth.Password}>
                                <Input variant={'filled'} placeholder={t('sftp password')} />
                            </Form.Item>
                        )
                    }
                    if (type === 1) {
                        return (
                            <>
                                <Form.Item name="private_key_type" label={t('sftp private key')} rules={rules.SFtp.Auth.PrivateKeyType}>
                                    <Radio.Group>
                                        <Radio value={0}>{t('content')}</Radio>
                                        <Radio value={1}>{t('path')}</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                <Form.Item noStyle shouldUpdate={(p, c) => get(p, 'private_key_type') !== get(c, 'private_key_type')}>
                                    {({ getFieldValue }) => {
                                        const pkType = getFieldValue('private_key_type');
                                        if (pkType === 0) {
                                            return (
                                                <Form.Item name="private_key_content" rules={rules.SFtp.Auth.PrivateKeyContent}>
                                                    <Input.TextArea variant={'filled'} rows={5} placeholder={t('please input the private key content')} />
                                                </Form.Item>
                                            )
                                        }
                                        if (pkType === 1) {
                                            return (
                                                <Form.Item name="private_key_path" rules={rules.SFtp.Auth.PrivateKeyPath}>
                                                    <Input variant={'filled'} placeholder={t('please input the private key path')} />
                                                </Form.Item>
                                            )
                                        }
                                        return null;
                                    }}
                                </Form.Item>
                                <Form.Item name="private_key_passphrase" label={t('private key password')}>
                                    <Input variant={'filled'} placeholder={t('private key password')} />
                                </Form.Item>
                            </>
                        )
                    }
                    return null;
                }}
            </Form.Item>
        </Form>
    )
}

const DicomModalForm = ({ form, rules, t }) => {
    return (
        <Form form={form} layout="vertical">
            <Form.Item name="server_ip" label={t('ip address')} rules={rules.Dicom.Auth.ServerIp}>
                <Input variant={'filled'} placeholder={t('please input ip addr')} />
            </Form.Item>
            <Form.Item name="server_port" label={t('dicom port')} rules={rules.Dicom.Auth.ServerPort}>
                <InputNumber variant={'filled'} min={0} precision={0} style={{ width: '100%' }} controls={false}
                    placeholder={t('please input dicom port')} />
            </Form.Item>
            <Form.Item name="server_aet" label={'Dicom AET'} rules={rules.Dicom.Auth.ServerAet}>
                <Input variant={'filled'} placeholder={t('please input dicom aet')} />
            </Form.Item>
        </Form>
    )
}


export default function ConfigGlobal() {
    const { t, i18n } = useTranslation();
    const formRules = useFormRules()

    const { message, modal } = App.useApp()
    const context = useContext(TransferInstance.rootContext)
    const [form] = Form.useForm()
    const [sftpModalForm] = Form.useForm()
    const [dicomModalForm] = Form.useForm()

    const [state, updateState] = useImmer({
        secretPairs: [],
        sftp: {
            modal: {
                open: false,
                editIndex: null,
            },
            // 备份ip配置，用于在切换解析类型时恢复数据
            backupIpConfigs: [],
            // 备份DNS配置，用于在切换解析类型时恢复数据
            backupDnsConfig: {}
        },
        dicom: {
            modal: {
                open: false,
                editIndex: null,
            },
            // 备份ip配置，用于在切换解析类型时恢复数据
            backupIpConfigs: [],
            // 备份DNS配置，用于在切换解析类型时恢复数据
            backupDnsConfig: {}
        }
    })

    const init = async () => {
        context.GlobalLoading(true)
        try {
            const { data } = await GetSysRemoteUploadConfig()
            if (!get(data, 'sftp.auth.resolve_type')) {
                set(data, 'sftp.auth.resolve_type', 'dns');
            }
            if (!get(data, 'dicom.auth.resolve_type')) {
                set(data, 'dicom.auth.resolve_type', 'dns');
            }
            set(data, 'sftp.upload.allowed_file_types', join(get(data, 'sftp.upload.allowed_file_types', []), ','))
            set(data, 'dicom.upload.allowed_file_types', join(get(data, 'dicom.upload.allowed_file_types', []), ','))
            form.setFieldsValue(data)

            updateState(draft => {
                draft.secretPairs = get(data, 'secret_pairs', [])
                // 备份初始的ip配置
                draft.sftp.backupIpConfigs = get(data, 'sftp.auth.ip_configs', [])
                draft.dicom.backupIpConfigs = get(data, 'dicom.auth.ip_configs', [])
                // 备份初始的DNS配置
                const sftpAuth = get(data, 'sftp.auth', {})
                draft.sftp.backupDnsConfig = {
                    host: sftpAuth.host,
                    dns_server: sftpAuth.dns_server,
                    port: sftpAuth.port,
                    username: sftpAuth.username,
                    timeout: sftpAuth.timeout,
                    type: sftpAuth.type,
                    password: sftpAuth.password,
                    private_key_type: sftpAuth.private_key_type,
                    private_key_content: sftpAuth.private_key_content,
                    private_key_path: sftpAuth.private_key_path,
                    private_key_passphrase: sftpAuth.private_key_passphrase
                }
                const dicomAuth = get(data, 'dicom.auth', {})
                draft.dicom.backupDnsConfig = {
                    server_ip: dicomAuth.server_ip,
                    dns_server: dicomAuth.dns_server,
                    server_port: dicomAuth.server_port,
                    server_aet: dicomAuth.server_aet
                }
            })
        } catch (e) {
        } finally {
            context.GlobalLoading(false)
        }
    }

    useEffect(() => {
        init().catch(() => {
        })
    }, [])

    const handleFormFinish = async () => {
        context.GlobalLoading(true, t('submitting'))
        try {
            const values = cloneDeep(form.getFieldsValue(true))
            set(values, 'sftp.upload.allowed_file_types', uniq(split(get(values, 'sftp.upload.allowed_file_types'), ',')))
            set(values, 'dicom.upload.allowed_file_types', uniq(split(get(values, 'dicom.upload.allowed_file_types'), ',')))
            await UpdateSysRemoteUploadConfig({ data: values })
            message.success(t('operate success'))
        } catch (e) {
            const msg = get(e, 'message', t('operate fail'))
            message.error(msg)
        } finally {
            context.GlobalLoading(false)
        }
    }

    const handleFormFinishFailed = (e) => {
        let msg = t('check form')
        if (isArray(e.errorFields) && e.errorFields.length > 0) {
            const firstField = e.errorFields[0]
            if (isArray(firstField.errors) && firstField.errors.length > 0) {
                msg = firstField.errors[0]
            }
        }
        modal.warning({
            title: msg,
            centered: true,
            maskClosable: true,
        })
    }

    // SFTP Modal Handlers
    const handleOpenSftpModal = (editIndex = null) => {
        const sftpAuthValues = form.getFieldValue(['sftp', 'auth']);
        let initialValues = { type: 0, private_key_type: 0 };

        if (editIndex !== null) {
            initialValues = get(sftpAuthValues, ['ip_configs', editIndex]);
        } else if (sftpAuthValues && sftpAuthValues.ip_configs && sftpAuthValues.ip_configs.length > 0) {
            const lastIp = cloneDeep(sftpAuthValues.ip_configs[sftpAuthValues.ip_configs.length - 1]);
            delete lastIp.host;
            initialValues = { ...initialValues, ...lastIp };
        }

        sftpModalForm.resetFields();
        sftpModalForm.setFieldsValue(initialValues);

        updateState(draft => {
            draft.sftp.modal.open = true;
            draft.sftp.modal.editIndex = editIndex;
        })
    }

    const handleSftpModalOk = async () => {
        try {
            await sftpModalForm.validateFields();
            const modalValues = sftpModalForm.getFieldsValue(true);
            const sftp = form.getFieldValue('sftp') || {};
            const sftpAuth = sftp.auth || {};
            const currentIpConfigs = sftpAuth.ip_configs || [];
            let newIpConfigs;

            const finalModalValues = {
                host: '',
                port: undefined,
                username: '',
                timeout: undefined,
                type: 0,
                password: '',
                private_key_type: 0,
                private_key_content: '',
                private_key_path: '',
                private_key_passphrase: '',
                ...modalValues
            };

            if (state.sftp.modal.editIndex !== null) {
                newIpConfigs = [...currentIpConfigs];
                newIpConfigs[state.sftp.modal.editIndex] = finalModalValues;
            } else {
                newIpConfigs = [...currentIpConfigs, finalModalValues];
            }

            form.setFieldsValue({
                sftp: {
                    ...sftp,
                    auth: {
                        ...sftpAuth,
                        ip_configs: newIpConfigs
                    }
                }
            });

            // 同步更新备份
            updateState(draft => {
                draft.sftp.backupIpConfigs = newIpConfigs
            })

            handleSftpModalCancel();
        } catch (e) {
            console.log('Validate Failed:', e);
        }
    };

    const handleSftpModalCancel = () => {
        updateState(draft => {
            draft.sftp.modal.open = false;
            draft.sftp.modal.editIndex = null;
        })
        sftpModalForm.resetFields();
    }

    // DICOM Modal Handlers
    const handleOpenDicomModal = (editIndex = null) => {
        const dicomAuthValues = form.getFieldValue(['dicom', 'auth']);
        let initialValues = {};

        if (editIndex !== null) {
            initialValues = get(dicomAuthValues, ['ip_configs', editIndex]);
        } else if (dicomAuthValues && dicomAuthValues.ip_configs && dicomAuthValues.ip_configs.length > 0) {
            const lastIp = cloneDeep(dicomAuthValues.ip_configs[dicomAuthValues.ip_configs.length - 1]);
            delete lastIp.server_ip;
            initialValues = { ...initialValues, ...lastIp };
        }

        dicomModalForm.resetFields();
        dicomModalForm.setFieldsValue(initialValues);

        updateState(draft => {
            draft.dicom.modal.open = true;
            draft.dicom.modal.editIndex = editIndex;
        })
    }

    const handleDicomModalOk = async () => {
        try {
            const modalValues = await dicomModalForm.validateFields();
            const dicom = form.getFieldValue('dicom') || {};
            const dicomAuth = dicom.auth || {};
            const currentIpConfigs = dicomAuth.ip_configs || [];
            let newIpConfigs;

            if (state.dicom.modal.editIndex !== null) {
                newIpConfigs = [...currentIpConfigs];
                newIpConfigs[state.dicom.modal.editIndex] = modalValues;
            } else {
                newIpConfigs = [...currentIpConfigs, modalValues];
            }

            form.setFieldsValue({
                dicom: {
                    ...dicom,
                    auth: {
                        ...dicomAuth,
                        ip_configs: newIpConfigs
                    }
                }
            });

            // 同步更新备份
            updateState(draft => {
                draft.dicom.backupIpConfigs = newIpConfigs
            })

            handleDicomModalCancel();
        } catch (e) {
            console.log('Validate Failed:', e);
        }
    };

    const handleDicomModalCancel = () => {
        updateState(draft => {
            draft.dicom.modal.open = false;
            draft.dicom.modal.editIndex = null;
        })
        dicomModalForm.resetFields();
    }

    // 删除SFTP IP配置的处理函数
    const handleRemoveSftpIpConfig = (index) => {
        const sftp = form.getFieldValue('sftp') || {};
        const sftpAuth = sftp.auth || {};
        const currentIpConfigs = sftpAuth.ip_configs || [];
        const newIpConfigs = currentIpConfigs.filter((_, i) => i !== index);

        form.setFieldsValue({
            sftp: {
                ...sftp,
                auth: {
                    ...sftpAuth,
                    ip_configs: newIpConfigs
                }
            }
        });

        // 同步更新备份
        updateState(draft => {
            draft.sftp.backupIpConfigs = newIpConfigs
        })
    }

    // 删除DICOM IP配置的处理函数
    const handleRemoveDicomIpConfig = (index) => {
        const dicom = form.getFieldValue('dicom') || {};
        const dicomAuth = dicom.auth || {};
        const currentIpConfigs = dicomAuth.ip_configs || [];
        const newIpConfigs = currentIpConfigs.filter((_, i) => i !== index);

        form.setFieldsValue({
            dicom: {
                ...dicom,
                auth: {
                    ...dicomAuth,
                    ip_configs: newIpConfigs
                }
            }
        });

        // 同步更新备份
        updateState(draft => {
            draft.dicom.backupIpConfigs = newIpConfigs
        })
    }

    const handleSftpResolveTypeChange = (e) => {
        const value = e.target.value;
        const currentSftpAuth = form.getFieldValue(['sftp', 'auth']) || {};

        if (value === 'dns') {
            // 切换到DNS解析时，先备份当前的ip_configs
            const currentIpConfigs = currentSftpAuth.ip_configs || [];
            updateState(draft => {
                draft.sftp.backupIpConfigs = currentIpConfigs;
            });

            // 恢复DNS配置
            form.setFieldsValue({
                sftp: {
                    auth: {
                        ...currentSftpAuth,
                        resolve_type: 'dns',
                        ip_configs: [],
                        ...state.sftp.backupDnsConfig
                    }
                }
            });
        } else if (value === 'ip') {
            // 切换到手动IP时，先备份当前的DNS配置
            updateState(draft => {
                draft.sftp.backupDnsConfig = {
                    host: currentSftpAuth.host,
                    dns_server: currentSftpAuth.dns_server,
                    port: currentSftpAuth.port,
                    username: currentSftpAuth.username,
                    timeout: currentSftpAuth.timeout,
                    type: currentSftpAuth.type,
                    password: currentSftpAuth.password,
                    private_key_type: currentSftpAuth.private_key_type,
                    private_key_content: currentSftpAuth.private_key_content,
                    private_key_path: currentSftpAuth.private_key_path,
                    private_key_passphrase: currentSftpAuth.private_key_passphrase
                };
            });

            // 清除DNS相关字段，恢复IP配置
            form.setFieldsValue({
                sftp: {
                    auth: {
                        ...currentSftpAuth,
                        resolve_type: 'ip',
                        // 清除DNS相关字段
                        host: undefined,
                        dns_server: undefined,
                        port: undefined,
                        username: undefined,
                        timeout: undefined,
                        type: undefined,
                        password: undefined,
                        private_key_type: undefined,
                        private_key_content: undefined,
                        private_key_path: undefined,
                        private_key_passphrase: undefined,
                        // 恢复IP配置
                        ip_configs: state.sftp.backupIpConfigs
                    }
                }
            });
        }
    }

    const handleDicomResolveTypeChange = (e) => {
        const value = e.target.value;
        const currentDicomAuth = form.getFieldValue(['dicom', 'auth']) || {};

        if (value === 'dns') {
            // 切换到DNS解析时，先备份当前的ip_configs
            const currentIpConfigs = currentDicomAuth.ip_configs || [];
            updateState(draft => {
                draft.dicom.backupIpConfigs = currentIpConfigs;
            });

            // 恢复DNS配置
            form.setFieldsValue({
                dicom: {
                    auth: {
                        ...currentDicomAuth,
                        resolve_type: 'dns',
                        ip_configs: [],
                        ...state.dicom.backupDnsConfig
                    }
                }
            });
        } else if (value === 'ip') {
            // 切换到手动IP时，先备份当前的DNS配置
            updateState(draft => {
                draft.dicom.backupDnsConfig = {
                    server_ip: currentDicomAuth.server_ip,
                    dns_server: currentDicomAuth.dns_server,
                    server_port: currentDicomAuth.server_port,
                    server_aet: currentDicomAuth.server_aet
                };
            });

            // 清除DNS相关字段，恢复IP配置
            form.setFieldsValue({
                dicom: {
                    auth: {
                        ...currentDicomAuth,
                        resolve_type: 'ip',
                        // 清除DNS相关字段
                        server_ip: undefined,
                        dns_server: undefined,
                        server_port: undefined,
                        server_aet: undefined,
                        // 恢复IP配置
                        ip_configs: state.dicom.backupIpConfigs
                    }
                }
            });
        }
    }


    return (
        <div className={styles.wrapper}>
            <div className={'wrapper-inner'}>
                <div className={'h-full'}>
                    <PageLayout header={t('remote upload settings')}>
                        <Form scrollToFirstError={{ behavior: 'smooth' }} labelCol={{ span: 3 }} form={form}
                            onFinish={handleFormFinish} onFinishFailed={handleFormFinishFailed}>
                            <Fieldset className={'sftp-config'} legend={t('sftp upload config')}>
                                <Form.Item name={['sftp', 'enabled']} label={t('sftp upload')} extra={
                                    <div className={'watermark-explain'}>
                                        <div className={'watermark-explain-head'}>
                                            <h2>{t('once you enable this function, all files meet the condition below will be uploaded to the sftp remote server')}</h2>
                                        </div>
                                    </div>
                                }>
                                    <Switch checkedChildren={t('open')} unCheckedChildren={t('close')} />
                                </Form.Item>
                                <Form.Item noStyle={true} shouldUpdate={(p, c) => get(p, 'sftp.enabled') !== get(c, 'sftp.enabled')}>
                                    {({ getFieldValue }) =>
                                        getFieldValue(['sftp', 'enabled']) ? (
                                            <>
                                                <Form.Item wrapperCol={{ offset: 3 }}>
                                                    <Card className={'auth-config'} title={t('authentication config')}>
                                                        <Form.Item labelCol={{ style: { width: '140px' } }}
                                                            labelAlign={'left'}
                                                            name={['sftp', 'auth', 'resolve_type']}
                                                            label={t('resolve type')}>
                                                            <Radio.Group onChange={handleSftpResolveTypeChange}>
                                                                <Radio value={'dns'}>{t('dns resolve')}</Radio>
                                                                <Radio value={'ip'}>{t('manual ip')}</Radio>
                                                            </Radio.Group>
                                                        </Form.Item>

                                                        <Form.Item noStyle shouldUpdate={(p, c) => get(p, 'sftp.auth.resolve_type') !== get(c, 'sftp.auth.resolve_type')}>
                                                            {({ getFieldValue }) => {
                                                                const resolveType = getFieldValue(['sftp', 'auth', 'resolve_type']);
                                                                if (resolveType === 'ip') {
                                                                    return (
                                                                        <Form.List name={['sftp', 'auth', 'ip_configs']}>
                                                                            {(fields) => (
                                                                                <>
                                                                                    <List
                                                                                        className={'ip-list'}
                                                                                        header={
                                                                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                                <b>{t('ip config list')}</b>
                                                                                                <Button type="primary" onClick={() => handleOpenSftpModal(null)}>{t('add ip')}</Button>
                                                                                            </div>
                                                                                        }
                                                                                        bordered
                                                                                        dataSource={fields}
                                                                                        renderItem={(_, index) => {
                                                                                            const ipConfig = form.getFieldValue(['sftp', 'auth', 'ip_configs', index]) || {};
                                                                                            return (
                                                                                                <List.Item
                                                                                                    actions={[
                                                                                                        <Button type="link" onClick={() => handleOpenSftpModal(index)}>{t('edit')}</Button>,
                                                                                                        <Button type="link" danger onClick={() => handleRemoveSftpIpConfig(index)}>{t('delete')}</Button>
                                                                                                    ]}
                                                                                                >
                                                                                                    <div className={'ip-config-content'}>
                                                                                                        <div className={'ip-field host-field'}>
                                                                                                            <div className={'field-label'}>{t('sftp host')}:</div>
                                                                                                            <div className={'field-value'}>{ipConfig.host}</div>
                                                                                                        </div>
                                                                                                        <div className={'ip-field port-field'}>
                                                                                                            <div className={'field-label'}>{t('sftp port')}:</div>
                                                                                                            <div className={'field-value'}>{ipConfig.port}</div>
                                                                                                        </div>
                                                                                                        <div className={'ip-field username-field'}>
                                                                                                            <div className={'field-label'}>{t('sftp username')}:</div>
                                                                                                            <div className={'field-value'}>{ipConfig.username}</div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </List.Item>
                                                                                            )
                                                                                        }}
                                                                                    />
                                                                                </>
                                                                            )}
                                                                        </Form.List>
                                                                    )
                                                                }
                                                                return (
                                                                    <>
                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'dns_server']} label={"DNS Server"}>
                                                                            <Input variant={'filled'} placeholder={t('optional, default: system')} />
                                                                        </Form.Item>
                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'host']} label={t('sftp host')} rules={formRules.SFtp.Auth.Host}>
                                                                            <Input variant={'filled'} placeholder={t('sftp host')} />
                                                                        </Form.Item>
                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'port']} label={t('sftp port')} rules={formRules.SFtp.Auth.Port}>
                                                                            <InputNumber min={0} precision={0} style={{ width: '138px' }} controls={false} variant={'filled'} placeholder={t('sftp port')} />
                                                                        </Form.Item>
                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'username']} label={t('sftp username')} rules={formRules.SFtp.Auth.Username}>
                                                                            <Input variant={'filled'} placeholder={t('sftp username')} />
                                                                        </Form.Item>
                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'timeout']} label={t('sftp connect timeout')} rules={formRules.SFtp.Auth.Timeout}>
                                                                            <InputNumber min={0} precision={0} style={{ width: '138px' }} suffix={'ms'} controls={false} variant={'filled'} placeholder={t('sftp connect timeout')} />
                                                                        </Form.Item>
                                                                        <Form.Item required={true} labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'type']} label={t('authentication method')}>
                                                                            <Radio.Group buttonStyle={'solid'}>
                                                                                <Radio.Button value={0}>{t('password authentication')}</Radio.Button>
                                                                                <Radio.Button value={1}>{t('private key authentication')}</Radio.Button>
                                                                            </Radio.Group>
                                                                        </Form.Item>
                                                                        <Form.Item noStyle shouldUpdate={(p, c) => get(p, 'sftp.auth.type') !== get(c, 'sftp.auth.type')}>
                                                                            {({ getFieldValue }) => {
                                                                                const type = getFieldValue(['sftp', 'auth', 'type'])
                                                                                if (typeof type === "undefined") return <></>
                                                                                return type === 0 ? (
                                                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'password']} label={t('sftp password')} rules={formRules.SFtp.Auth.Password}>
                                                                                        <Input variant={'filled'} placeholder={t('sftp password')} />
                                                                                    </Form.Item>
                                                                                ) : (
                                                                                    <>
                                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'private_key_type']} required={true} label={t('sftp private key')} style={{ marginBottom: '10px' }}>
                                                                                            <Radio.Group>
                                                                                                <Radio value={0}>{t('content')}</Radio>
                                                                                                <Radio value={1}>{t('path')}</Radio>
                                                                                            </Radio.Group>
                                                                                        </Form.Item>
                                                                                        <Form.Item noStyle shouldUpdate={(p, c) => get(p, 'sftp.auth.private_key_type') !== get(c, 'sftp.auth.private_key_type')}>
                                                                                            {({ getFieldValue }) => getFieldValue(['sftp', 'auth', 'private_key_type']) === 0 ? (
                                                                                                <Form.Item wrapperCol={{ style: { marginLeft: '140px' } }} name={['sftp', 'auth', 'private_key_content']} rules={formRules.SFtp.Auth.PrivateKeyContent}>
                                                                                                    <Input.TextArea rows={5} variant={'filled'} placeholder={t('please input the private key content')} />
                                                                                                </Form.Item>
                                                                                            ) : (
                                                                                                <Form.Item wrapperCol={{ style: { marginLeft: '140px' } }} name={['sftp', 'auth', 'private_key_path']} rules={formRules.SFtp.Auth.PrivateKeyPath}>
                                                                                                    <Input variant={'filled'} placeholder={t('please input the private key path')} />
                                                                                                </Form.Item>
                                                                                            )}
                                                                                        </Form.Item>
                                                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'auth', 'private_key_passphrase']} label={t('private key password')}>
                                                                                            <Input variant={'filled'} placeholder={t('private key password')} />
                                                                                        </Form.Item>
                                                                                    </>
                                                                                )
                                                                            }}
                                                                        </Form.Item>
                                                                    </>
                                                                )
                                                            }}
                                                        </Form.Item>
                                                    </Card>
                                                    <Card className={'upload-config'} title={t('upload config')}>
                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'upload', 'dst_dir']} label={t('dst dir')} required={true} rules={formRules.SFtp.Upload.dst_dir}>
                                                            <Input variant={'filled'} placeholder={t('please input the dst dir')} />
                                                        </Form.Item>
                                                        <Form.Item hidden={true} labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['sftp', 'upload', 'allowed_file_types']} label={t('allowed file types')} rules={formRules.SFtp.Upload.AllowedFileTypes}>
                                                            <Input variant={'filled'} placeholder={t('please input the allowed file types')} />
                                                        </Form.Item>
                                                        <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} label={t('delete after upload')} name={['sftp', 'upload', 'delete_after_upload']}>
                                                            <Switch />
                                                        </Form.Item>
                                                    </Card>
                                                </Form.Item>

                                                <Modal title={state.sftp.modal.editIndex !== null ? t('edit sftp ip config') : t('add sftp ip config')}
                                                    open={state.sftp.modal.open}
                                                    onOk={handleSftpModalOk}
                                                    onCancel={handleSftpModalCancel}
                                                    destroyOnClose={true}
                                                    maskClosable={false}
                                                    forceRender
                                                >
                                                    <SftpModalForm form={sftpModalForm} rules={formRules} t={t} />
                                                </Modal>
                                            </>
                                        ) : null
                                    }
                                </Form.Item>
                            </Fieldset>
                            <Fieldset legend={t('dicom upload config')} className={'dicom-config'}>
                                <Form.Item name={['dicom', 'enabled']} label={t('dicom upload')} extra={
                                    <div className={'watermark-explain'}>
                                        <div className={'watermark-explain-head'}>
                                            <h2>{t('dicom function explain')}</h2>
                                        </div>
                                    </div>
                                }>
                                    <Switch checkedChildren={t('open')} unCheckedChildren={t('close')} />
                                </Form.Item>
                                <Form.Item noStyle={true} shouldUpdate={(p, c) => get(p, 'dicom.enabled') !== get(c, 'dicom.enabled')}>
                                    {({ getFieldValue }) => getFieldValue(['dicom', 'enabled']) ? (
                                        <>
                                            <Form.Item wrapperCol={{ offset: 3 }}>
                                                <Card className={'auth-config'} title={t('authentication config')}>
                                                    <Form.Item labelCol={{ style: { width: '140px' } }}
                                                        labelAlign={'left'}
                                                        name={['dicom', 'auth', 'resolve_type']}
                                                        label={t('resolve type')}>
                                                        <Radio.Group onChange={handleDicomResolveTypeChange}>
                                                            <Radio value={'dns'}>{t('dns resolve')}</Radio>
                                                            <Radio value={'ip'}>{t('manual ip')}</Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                    <Form.Item noStyle shouldUpdate={(p, c) => get(p, 'dicom.auth.resolve_type') !== get(c, 'dicom.auth.resolve_type')}>
                                                        {({ getFieldValue }) => {
                                                            const resolveType = getFieldValue(['dicom', 'auth', 'resolve_type']);
                                                            if (resolveType === 'ip') {
                                                                return (
                                                                    <Form.List name={['dicom', 'auth', 'ip_configs']}>
                                                                        {(fields) => (
                                                                            <>
                                                                                <List
                                                                                    className={'ip-list'}
                                                                                    header={
                                                                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                            <b>{t('ip config list')}</b>
                                                                                            <Button type="primary" onClick={() => handleOpenDicomModal(null)}>{t('add ip')}</Button>
                                                                                        </div>
                                                                                    }
                                                                                    bordered
                                                                                    dataSource={fields}
                                                                                    renderItem={(_, index) => {
                                                                                        const ipConfig = form.getFieldValue(['dicom', 'auth', 'ip_configs', index]) || {};
                                                                                        return (
                                                                                            <List.Item
                                                                                                actions={[
                                                                                                    <Button type="link" onClick={() => handleOpenDicomModal(index)}>{t('edit')}</Button>,
                                                                                                    <Button type="link" danger onClick={() => handleRemoveDicomIpConfig(index)}>{t('delete')}</Button>
                                                                                                ]}
                                                                                            >
                                                                                                <div className={'ip-config-content'}>
                                                                                                    <div className={'ip-field host-field'}>
                                                                                                        <div className={'field-label'}>{t('ip address label')}</div>
                                                                                                        <div className={'field-value'}>{ipConfig.server_ip}</div>
                                                                                                    </div>
                                                                                                    <div className={'ip-field port-field'}>
                                                                                                        <div className={'field-label'}>{t('dicom port')}:</div>
                                                                                                        <div className={'field-value'}>{ipConfig.server_port}</div>
                                                                                                    </div>
                                                                                                    <div className={'ip-field aet-field'}>
                                                                                                        <div className={'field-label'}>{t('aet label')}</div>
                                                                                                        <div className={'field-value'}>{ipConfig.server_aet}</div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </List.Item>
                                                                                        )
                                                                                    }}
                                                                                />
                                                                            </>
                                                                        )}
                                                                    </Form.List>
                                                                )
                                                            }
                                                            return (
                                                                <>
                                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'auth', 'dns_server']} label={"DNS Server"}>
                                                                        <Input variant={'filled'} placeholder={t('optional, default: system')} />
                                                                    </Form.Item>
                                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'auth', 'server_ip']} label={'Dicom IP'} rules={formRules.Dicom.Auth.ServerIp}>
                                                                        <Input variant={'filled'} placeholder={t('please input dicom ip')} />
                                                                    </Form.Item>
                                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'auth', 'server_port']} label={t('dicom port')} rules={formRules.Dicom.Auth.ServerPort}>
                                                                        <InputNumber min={0} precision={0} style={{ width: '138px' }} controls={false} variant={'filled'} placeholder={t('please input dicom port')} />
                                                                    </Form.Item>
                                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'auth', 'server_aet']} label={'Dicom AET'} rules={formRules.Dicom.Auth.ServerAet}>
                                                                        <Input variant={'filled'} placeholder={t('please input dicom aet')} />
                                                                    </Form.Item>
                                                                </>
                                                            )
                                                        }}
                                                    </Form.Item>
                                                </Card>
                                                <Card className={'upload-config'} title={t('upload config')}>
                                                    <Form.Item hidden={true} labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'upload', 'allowed_file_types']} label={t('allowed file types')} rules={formRules.Dicom.Upload.AllowedFileTypes}>
                                                        <Input type={'hidden'} variant={'filled'} placeholder={t('please input the allowed file types')} />
                                                    </Form.Item>
                                                    <Form.Item labelCol={{ style: { width: '140px' } }} labelAlign={'left'} name={['dicom', 'upload', 'dst_server']} label={t('upload address')} rules={formRules.Dicom.Upload.UploadAddress}>
                                                        <Input variant={'filled'} placeholder={t('please input the upload address')} />
                                                    </Form.Item>
                                                </Card>
                                            </Form.Item>

                                            <Modal title={state.dicom.modal.editIndex !== null ? t('edit dicom ip config') : t('add dicom ip config')}
                                                open={state.dicom.modal.open}
                                                onOk={handleDicomModalOk}
                                                onCancel={handleDicomModalCancel}
                                                destroyOnClose={true}
                                                maskClosable={false}
                                                forceRender
                                            >
                                                <DicomModalForm form={dicomModalForm} rules={formRules} t={t} />
                                            </Modal>
                                        </>
                                    ) : null}
                                </Form.Item>
                            </Fieldset>
                            <Form.Item wrapperCol={{ offset: 3 }}>
                                <div className={'actions'}>
                                    <Button onClick={form.submit} type={'primary'}
                                        className={'act-submit'}>{t('submit')}</Button>
                                </div>
                            </Form.Item>
                        </Form>
                    </PageLayout>
                </div>
            </div>
        </div>
    )
}